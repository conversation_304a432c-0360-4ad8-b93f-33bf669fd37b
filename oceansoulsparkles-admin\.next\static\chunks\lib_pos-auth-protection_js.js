"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib_pos-auth-protection_js"],{

/***/ "./lib/pos-auth-protection.js":
/*!************************************!*\
  !*** ./lib/pos-auth-protection.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSecurityLockout: function() { return /* binding */ clearSecurityLockout; },\n/* harmony export */   endPOSPaymentOperation: function() { return /* binding */ endPOSPaymentOperation; },\n/* harmony export */   generateSecurePaymentReference: function() { return /* binding */ generateSecurePaymentReference; },\n/* harmony export */   isPOSPaymentOperationActive: function() { return /* binding */ isPOSPaymentOperationActive; },\n/* harmony export */   isSecurityLockoutActive: function() { return /* binding */ isSecurityLockoutActive; },\n/* harmony export */   logPOSSecurityEvent: function() { return /* binding */ logPOSSecurityEvent; },\n/* harmony export */   sanitizePaymentMetadata: function() { return /* binding */ sanitizePaymentMetadata; },\n/* harmony export */   startPOSPaymentOperation: function() { return /* binding */ startPOSPaymentOperation; },\n/* harmony export */   triggerSecurityLockout: function() { return /* binding */ triggerSecurityLockout; },\n/* harmony export */   validatePOSPaymentRequest: function() { return /* binding */ validatePOSPaymentRequest; }\n/* harmony export */ });\n/**\n * POS Authentication Protection Module\n * Provides security measures for POS payment operations\n */ let paymentOperationActive = false;\nlet paymentTimeout = null;\nlet securityLockout = false;\n/**\n * Start POS payment operation with security measures\n */ function startPOSPaymentOperation() {\n    try {\n        if (securityLockout) {\n            throw new Error(\"POS system is temporarily locked due to security measures\");\n        }\n        paymentOperationActive = true;\n        // Set timeout for payment operation (5 minutes)\n        paymentTimeout = setTimeout(()=>{\n            endPOSPaymentOperation();\n            console.warn(\"POS payment operation timed out for security\");\n        }, 5 * 60 * 1000);\n        console.log(\"POS payment operation started with security protection\");\n        return true;\n    } catch (error) {\n        console.error(\"Error starting POS payment operation:\", error);\n        return false;\n    }\n}\n/**\n * End POS payment operation and clear security measures\n */ function endPOSPaymentOperation() {\n    try {\n        paymentOperationActive = false;\n        if (paymentTimeout) {\n            clearTimeout(paymentTimeout);\n            paymentTimeout = null;\n        }\n        console.log(\"POS payment operation ended\");\n        return true;\n    } catch (error) {\n        console.error(\"Error ending POS payment operation:\", error);\n        return false;\n    }\n}\n/**\n * Check if POS payment operation is currently active\n */ function isPOSPaymentOperationActive() {\n    return paymentOperationActive;\n}\n/**\n * Validate POS payment request for security\n */ function validatePOSPaymentRequest(paymentData) {\n    try {\n        var _paymentData_description;\n        // Basic validation\n        if (!paymentData) {\n            throw new Error(\"Payment data is required\");\n        }\n        if (!paymentData.amount || paymentData.amount <= 0) {\n            throw new Error(\"Valid payment amount is required\");\n        }\n        if (paymentData.amount > 10000) {\n            throw new Error(\"Payment amount exceeds maximum limit\");\n        }\n        // Check for suspicious patterns\n        if (paymentData.amount === 9999.99) {\n            console.warn(\"Suspicious payment amount detected\");\n        }\n        return {\n            valid: true,\n            sanitizedData: {\n                amount: Math.round(paymentData.amount * 100) / 100,\n                currency: paymentData.currency || \"AUD\",\n                description: ((_paymentData_description = paymentData.description) === null || _paymentData_description === void 0 ? void 0 : _paymentData_description.substring(0, 100)) || \"\",\n                metadata: paymentData.metadata || {}\n            }\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: error.message\n        };\n    }\n}\n/**\n * Log POS security event\n */ function logPOSSecurityEvent(event) {\n    let details = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    try {\n        const securityLog = {\n            timestamp: new Date().toISOString(),\n            event,\n            details,\n            userAgent:  true ? window.navigator.userAgent : 0,\n            ip: \"unknown\" // Would be populated by server-side logging\n        };\n        console.log(\"POS Security Event:\", securityLog);\n        // In production, this would send to a security monitoring service\n        return true;\n    } catch (error) {\n        console.error(\"Error logging POS security event:\", error);\n        return false;\n    }\n}\n/**\n * Trigger security lockout (emergency measure)\n */ function triggerSecurityLockout() {\n    let reason = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"Security violation detected\";\n    try {\n        securityLockout = true;\n        endPOSPaymentOperation();\n        logPOSSecurityEvent(\"SECURITY_LOCKOUT\", {\n            reason\n        });\n        // Auto-unlock after 30 minutes\n        setTimeout(()=>{\n            securityLockout = false;\n            logPOSSecurityEvent(\"SECURITY_LOCKOUT_CLEARED\", {\n                reason: \"Automatic timeout\"\n            });\n        }, 30 * 60 * 1000);\n        console.error(\"POS security lockout triggered:\", reason);\n        return true;\n    } catch (error) {\n        console.error(\"Error triggering security lockout:\", error);\n        return false;\n    }\n}\n/**\n * Check if system is in security lockout\n */ function isSecurityLockoutActive() {\n    return securityLockout;\n}\n/**\n * Clear security lockout (admin override)\n */ function clearSecurityLockout() {\n    try {\n        securityLockout = false;\n        logPOSSecurityEvent(\"SECURITY_LOCKOUT_CLEARED\", {\n            reason: \"Admin override\"\n        });\n        console.log(\"POS security lockout cleared by admin\");\n        return true;\n    } catch (error) {\n        console.error(\"Error clearing security lockout:\", error);\n        return false;\n    }\n}\n/**\n * Sanitize payment metadata for security\n */ function sanitizePaymentMetadata(metadata) {\n    try {\n        if (!metadata || typeof metadata !== \"object\") {\n            return {};\n        }\n        const sanitized = {};\n        const allowedKeys = [\n            \"booking_id\",\n            \"customer_id\",\n            \"service_id\",\n            \"artist_id\",\n            \"notes\"\n        ];\n        for (const key of allowedKeys){\n            if (metadata[key]) {\n                // Sanitize string values\n                if (typeof metadata[key] === \"string\") {\n                    sanitized[key] = metadata[key].substring(0, 255).replace(/[<>]/g, \"\");\n                } else {\n                    sanitized[key] = metadata[key];\n                }\n            }\n        }\n        return sanitized;\n    } catch (error) {\n        console.error(\"Error sanitizing payment metadata:\", error);\n        return {};\n    }\n}\n/**\n * Generate secure payment reference\n */ function generateSecurePaymentReference() {\n    try {\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 15);\n        return \"OSS-\".concat(timestamp, \"-\").concat(random).toUpperCase();\n    } catch (error) {\n        console.error(\"Error generating payment reference:\", error);\n        return \"OSS-\".concat(Date.now(), \"-ERROR\");\n    }\n}\n// Default export for backward compatibility\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n    startPOSPaymentOperation,\n    endPOSPaymentOperation,\n    isPOSPaymentOperationActive,\n    validatePOSPaymentRequest,\n    logPOSSecurityEvent,\n    triggerSecurityLockout,\n    isSecurityLockoutActive,\n    clearSecurityLockout,\n    sanitizePaymentMetadata,\n    generateSecurePaymentReference\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/pos-auth-protection.js\n"));

/***/ })

}]);